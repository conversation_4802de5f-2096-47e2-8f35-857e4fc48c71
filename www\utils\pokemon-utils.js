// utils/pokemon-utils.js
// Utility functions for Pokemon operations

import { logger } from './logger.js';
import { gameState } from '../state/game-state.js';

/**
 * Find a Pokemon in the pokedex data by name or dex number
 * @param {string} name - The Pokemon name to search for
 * @param {number} dexNumber - The Pokemon dex number to search for (optional)
 * @param {Array} customPokedexData - Optional custom pokedex data to use instead of gameState.pokedexData
 * @returns {Object|null} - The found Pokemon entry or null if not found
 */
export function findPokemonInPokedex(name, dexNumber = null, customPokedexData = null) {
  // Get pokedex data from various sources
  const pokedexData = customPokedexData || gameState.pokedexData || window.pokedexData;
  
  if (!pokedexData || !Array.isArray(pokedexData) || pokedexData.length === 0) {
    logger.warn('No pokedex data available for lookup');
    return null;
  }
  
  let entry = null;
  
  // First try to find by dex_number if provided
  if (dexNumber) {
    entry = pokedexData.find(p => p.dex_number === dexNumber);
    if (entry) {
      logger.debug(`Found Pokemon by dex_number ${dexNumber}: ${entry.name}`);
      return entry;
    }
  }
  
  // Then try to find by exact name match (case-insensitive)
  if (name) {
    const nameLower = name.toLowerCase();
    entry = pokedexData.find(p => 
      p.name.toLowerCase() === nameLower ||
      (p.de && p.de.toLowerCase() === nameLower)
    );
    
    if (entry) {
      logger.debug(`Found Pokemon by exact name match: ${entry.name}`);
      return entry;
    }
    
    // If still not found, try more flexible matching
    for (const p of pokedexData) {
      // Check if the name is a substring of the pokedex entry name
      // or if the pokedex entry name is a substring of the name
      if (p.name.toLowerCase().includes(nameLower) ||
          nameLower.includes(p.name.toLowerCase()) ||
          (p.de && p.de.toLowerCase().includes(nameLower)) ||
          (p.de && nameLower.includes(p.de.toLowerCase()))) {
        
        logger.debug(`Found Pokemon by flexible name matching: ${p.name} for ${name}`);
        return p;
      }
    }
  }
  
  // Not found
  logger.debug(`Pokemon not found in pokedex: name=${name}, dex_number=${dexNumber}`);
  return null;
}

/**
 * Update a Pokemon with data from the pokedex
 * @param {Object} pokemon - The Pokemon object to update
 * @param {Array} customPokedexData - Optional custom pokedex data to use
 * @returns {boolean} - Whether the Pokemon was updated
 */
export function updatePokemonFromPokedex(pokemon, customPokedexData = null) {
  if (!pokemon) return false;
  
  // Find the Pokemon in the pokedex
  const entry = findPokemonInPokedex(
    pokemon.base_name || pokemon.name,
    pokemon.dex_number,
    customPokedexData
  );
  
  if (!entry) return false;
  
  // Update Pokemon with data from pokedex
  let updated = false;
  
  // Update dex_number if not set
  if (!pokemon.dex_number) {
    pokemon.dex_number = entry.dex_number;
    updated = true;
    logger.debug(`Updated dex_number to ${pokemon.dex_number} for ${pokemon.name}`);
  }
  
  // Update types if not set
  if (!pokemon.types || pokemon.types.length === 0) {
    pokemon.types = [...entry.types];
    updated = true;
    logger.debug(`Updated types to [${pokemon.types.join(', ')}] for ${pokemon.name}`);
  }
  
  // Update image if not set
  if (!pokemon.image && entry.image_url) {
    pokemon.image = entry.image_url;
    updated = true;
    logger.debug(`Updated image to ${pokemon.image} for ${pokemon.name}`);
  }
  
  // Update base_sprite if not set
  if (!pokemon.base_sprite && entry.image_url) {
    pokemon.base_sprite = entry.image_url;
    updated = true;
  }
  
  // Update image_url if not set
  if (!pokemon.image_url && entry.image_url) {
    pokemon.image_url = entry.image_url;
    updated = true;
  }
  
  // Update evolution data if not set
  if (!pokemon.evolution_level && entry.evolution_level) {
    pokemon.evolution_level = entry.evolution_level;
    updated = true;
  }
  
  if (!pokemon.evolution_chain_id && entry.evolution_chain_id) {
    pokemon.evolution_chain_id = entry.evolution_chain_id;
    updated = true;
  }
  
  // Update rarity if not set
  if (!pokemon.rarity && entry.rarity) {
    pokemon.rarity = entry.rarity;
    updated = true;
  }
  
  // Update base_name if it doesn't match
  if (pokemon.base_name !== entry.name) {
    pokemon.base_name = entry.name;
    updated = true;
    logger.debug(`Updated base_name to ${pokemon.base_name} for ${pokemon.name}`);
  }
  
  return updated;
}

/**
 * Generate HTML for a type icon image
 * @param {string} type - The Pokémon type (e.g., 'electric', 'fire')
 * @returns {string} - HTML string with img tag for the type icon
 */
export function getTypeIconHtml(type) {
  // Guard against non-string or empty inputs
  if (typeof type !== 'string' || !type.trim()) {
    return '';
  }

  // Trim the value before lowercasing to build the path
  const typeLower = type.trim().toLowerCase();
  const iconPath = `./icons/types/${typeLower}-type.svg`;

  return `<img src="${iconPath}" alt="${type}" class="type-icon" width="16" height="16">`;
}

export function renderTypeBadges(types = []) {
  return (types || []).map(t => {
    const typeKey = String(t).trim().toLowerCase();
    return `<span class="type-label type-${typeKey}">${getTypeIconHtml(t)}</span>`;
  }).join('');
}
